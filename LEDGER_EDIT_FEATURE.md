# ميزة التعديل المباشر في تقرير الدفتر العام

## نظرة عامة
تم إضافة ميزة جديدة لتعديل مبالغ المدين والدائن مباشرة في صفحة تقرير الدفتر العام `/ledger-report/` دون الحاجة للانتقال إلى صفحة منفصلة.

## الملفات المُعدلة

### 1. Routes (routes/web.php)
```php
// إضافة route جديد لتعديل المعاملات في تقرير الدفتر العام
Route::post('ledger-report/update-transaction', [ReportController::class, 'updateLedgerTransaction'])->name('report.ledger.update');
```

### 2. Controller (app/Http/Controllers/ReportController.php)
- إضافة دالة `updateLedgerTransaction()` للتعامل مع طلبات التعديل
- التحقق من الصلاحيات والبيانات
- تسجيل العمليات في سجل الأنشطة

### 3. Model (app/Models/Utility.php)
- تعديل دالة `getAccountData()` لتضمين معرف المعاملة (`transaction_line_id`)

### 4. Model (app/Models/TransactionLines.php)
- إضافة الحقول القابلة للتعبئة (`fillable`)
- تحديد أنواع البيانات (`casts`)
- إضافة العلاقة مع جدول الحسابات

### 5. View (resources/views/report/ledger_summary.blade.php)
- إضافة عمود "Actions" في رأس الجدول
- تحويل جميع صفوف المعاملات لتدعم التعديل المباشر
- إضافة أزرار التعديل والحفظ والإلغاء
- إضافة JavaScript للتعامل مع التعديل المباشر

## كيفية الاستخدام

### 1. الوصول للميزة
- انتقل إلى صفحة تقرير الدفتر العام: `/ledger-report/`
- اختر الحساب والفترة الزمنية المطلوبة

### 2. تعديل المعاملة
1. انقر على زر التعديل (أيقونة القلم) في العمود الأخير
2. ستتحول خانتا المدين والدائن إلى حقول إدخال
3. أدخل القيم الجديدة
4. انقر على زر الحفظ (أيقونة الحفظ) أو الإلغاء (أيقونة X)

### 3. التحديث التلقائي
- بعد الحفظ الناجح، ستتحدث الصفحة تلقائياً لإظهار الأرصدة الجديدة
- يتم عرض رسالة تأكيد النجاح

## الأمان والصلاحيات

### التحقق من الصلاحيات
- يتم التحقق من صلاحية "ledger report" قبل السماح بالتعديل
- التأكد من أن المعاملة تخص المستخدم الحالي فقط

### التحقق من البيانات
```php
$request->validate([
    'transaction_id' => 'required|integer|exists:transaction_lines,id',
    'debit' => 'required|numeric|min:0',
    'credit' => 'required|numeric|min:0',
]);
```

### تسجيل العمليات
- يتم تسجيل كل عملية تعديل في سجل الأنشطة
- حفظ القيم القديمة والجديدة للمراجعة

## الميزات التقنية

### AJAX Integration
- استخدام AJAX لتعديل البيانات دون إعادة تحميل الصفحة
- معالجة الأخطاء وعرض الرسائل المناسبة

### User Experience
- واجهة سهلة الاستخدام مع أزرار واضحة
- إمكانية الإلغاء قبل الحفظ
- تحديث فوري للبيانات

### CSS Styling
- تصميم متجاوب للأزرار
- حقول إدخال مناسبة الحجم
- محاذاة صحيحة للأرقام

## أنواع المعاملات المدعومة
- Invoice (فواتير المبيعات)
- Invoice Payment (مدفوعات الفواتير)
- Revenue (الإيرادات)
- Bill (فواتير المشتريات)
- Bill Payment (مدفوعات فواتير المشتريات)
- Payment (المدفوعات)
- POS (مبيعات نقاط البيع)
- EXP (المصروفات)
- Journal (قيود اليومية)

## الكود المحفوظ
تم الحفاظ على جميع الأكواد القديمة كتعليقات لضمان إمكانية الرجوع إليها عند الحاجة.

## التطوير المستقبلي
- إمكانية إضافة تعديل مجمع لعدة معاملات
- إضافة تاريخ آخر تعديل
- تحسين واجهة المستخدم
- إضافة تصدير التقرير مع التعديلات
