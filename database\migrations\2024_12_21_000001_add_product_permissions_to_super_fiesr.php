<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // البحث عن دور SUPER FIESR
            $superFiesrRole = Role::where('name', 'SUPER FIESR')->first();
            
            if ($superFiesrRole) {
                // الصلاحيات المطلوبة لإدارة المنتجات
                $productPermissions = [
                    'manage product & service',
                    'create product & service',
                    'edit product & service',
                    'delete product & service',
                    'show product & service',
                ];
                
                // التحقق من وجود الصلاحيات وإنشاؤها إذا لم تكن موجودة
                foreach ($productPermissions as $permissionName) {
                    $permission = Permission::firstOrCreate([
                        'name' => $permissionName,
                        'guard_name' => 'web'
                    ]);
                    
                    // إعطاء الصلاحية للدور إذا لم يكن يملكها
                    if (!$superFiesrRole->hasPermissionTo($permission)) {
                        $superFiesrRole->givePermissionTo($permission);
                    }
                }
                
                echo "تم إضافة صلاحيات إدارة المنتجات لدور SUPER FIESR بنجاح.\n";
            } else {
                echo "لم يتم العثور على دور SUPER FIESR.\n";
            }
            
        } catch (\Exception $e) {
            echo "خطأ في إضافة الصلاحيات: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try {
            // البحث عن دور SUPER FIESR
            $superFiesrRole = Role::where('name', 'SUPER FIESR')->first();
            
            if ($superFiesrRole) {
                // الصلاحيات المراد إزالتها
                $productPermissions = [
                    'manage product & service',
                    'create product & service',
                    'edit product & service',
                    'delete product & service',
                    'show product & service',
                ];
                
                // إزالة الصلاحيات من الدور
                foreach ($productPermissions as $permissionName) {
                    $permission = Permission::where('name', $permissionName)->first();
                    if ($permission && $superFiesrRole->hasPermissionTo($permission)) {
                        $superFiesrRole->revokePermissionTo($permission);
                    }
                }
                
                echo "تم إزالة صلاحيات إدارة المنتجات من دور SUPER FIESR.\n";
            }
            
        } catch (\Exception $e) {
            echo "خطأ في إزالة الصلاحيات: " . $e->getMessage() . "\n";
        }
    }
};
