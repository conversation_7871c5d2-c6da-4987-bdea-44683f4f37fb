@extends('layouts.admin')
@section('page-title')
    {{ __('Ledger Summary') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Ledger Summary') }}</li>
@endsection

{{-- إضافة CSRF token للطلبات AJAX --}}
@push('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        /* تحسينات CSS للأزرار والتعديل المباشر */
        .edit-btn, .save-btn, .cancel-btn {
            margin: 2px;
            padding: 4px 8px;
            font-size: 12px;
        }

        .debit-amount input, .credit-amount input {
            width: 100px;
            text-align: right;
        }

        .table td {
            vertical-align: middle;
        }

        .btn-group-sm > .btn, .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }
    </style>
@endpush
@push('script-page')
    <script type="text/javascript" src="{{ asset('js/html2pdf.bundle.min.js') }}"></script>
    <script>
        var filename = $('#filename').val();

        function saveAsPDF() {
            var element = document.getElementById('printableArea');
            var opt = {
                margin: 0.3,
                filename: filename,
                image: {
                    type: 'jpeg',
                    quality: 1
                },
                html2canvas: {
                    scale: 4,
                    dpi: 72,
                    letterRendering: true
                },
                jsPDF: {
                    unit: 'in',
                    format: 'A2'
                }
            };
            html2pdf().set(opt).from(element).save();
        }

        // دالة جديدة لتعديل المعاملات مباشرة
        function enableEdit(transactionId) {
            // الكود الجديد - التحقق من صحة معرف المعاملة قبل التعديل
            if (!transactionId || transactionId === 0 || transactionId === '0') {
                alert('{{ __("Transaction ID is not valid for editing.") }}');
                return;
            }

            const row = document.querySelector(`tr[data-transaction-id="${transactionId}"]`);

            // التحقق من أن الصف غير مخصص للتعديل
            if (row && row.getAttribute('data-no-edit') === 'true') {
                alert('{{ __("This transaction cannot be edited.") }}');
                return;
            }
            const debitCell = row.querySelector('.debit-amount');
            const creditCell = row.querySelector('.credit-amount');
            const editBtn = row.querySelector('.edit-btn');
            const saveBtn = row.querySelector('.save-btn');
            const cancelBtn = row.querySelector('.cancel-btn');

            // حفظ القيم الأصلية
            const originalDebit = debitCell.textContent.replace(/[^\d.-]/g, '');
            const originalCredit = creditCell.textContent.replace(/[^\d.-]/g, '');

            debitCell.setAttribute('data-original', originalDebit);
            creditCell.setAttribute('data-original', originalCredit);

            // تحويل إلى حقول إدخال
            debitCell.innerHTML = `<input type="number" class="form-control form-control-sm" value="${originalDebit}" step="0.01" min="0">`;
            creditCell.innerHTML = `<input type="number" class="form-control form-control-sm" value="${originalCredit}" step="0.01" min="0">`;

            // إظهار/إخفاء الأزرار
            editBtn.style.display = 'none';
            saveBtn.style.display = 'inline-block';
            cancelBtn.style.display = 'inline-block';
        }

        function cancelEdit(transactionId) {
            const row = document.querySelector(`tr[data-transaction-id="${transactionId}"]`);
            const debitCell = row.querySelector('.debit-amount');
            const creditCell = row.querySelector('.credit-amount');
            const editBtn = row.querySelector('.edit-btn');
            const saveBtn = row.querySelector('.save-btn');
            const cancelBtn = row.querySelector('.cancel-btn');

            // استرجاع القيم الأصلية
            const originalDebit = debitCell.getAttribute('data-original');
            const originalCredit = creditCell.getAttribute('data-original');

            debitCell.innerHTML = parseFloat(originalDebit).toLocaleString('en-US', {minimumFractionDigits: 2});
            creditCell.innerHTML = parseFloat(originalCredit).toLocaleString('en-US', {minimumFractionDigits: 2});

            // إظهار/إخفاء الأزرار
            editBtn.style.display = 'inline-block';
            saveBtn.style.display = 'none';
            cancelBtn.style.display = 'none';
        }

        function saveTransaction(transactionId) {
            // الكود الجديد - التحقق من صحة معرف المعاملة قبل الحفظ
            if (!transactionId || transactionId === 0 || transactionId === '0') {
                alert('{{ __("Transaction ID is not valid for saving.") }}');
                return;
            }

            const row = document.querySelector(`tr[data-transaction-id="${transactionId}"]`);

            // التحقق من أن الصف غير مخصص للتعديل
            if (row && row.getAttribute('data-no-edit') === 'true') {
                alert('{{ __("This transaction cannot be saved.") }}');
                return;
            }
            const debitInput = row.querySelector('.debit-amount input');
            const creditInput = row.querySelector('.credit-amount input');

            const debitValue = parseFloat(debitInput.value) || 0;
            const creditValue = parseFloat(creditInput.value) || 0;

            // إرسال البيانات عبر AJAX
            // الكود الجديد - التحقق من وجود CSRF token قبل الإرسال
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                alert('{{ __("CSRF token not found. Please refresh the page.") }}');
                return;
            }

            console.log('Sending request with data:', {
                transaction_id: transactionId,
                debit: debitValue,
                credit: creditValue
            });

            fetch('{{ route("report.ledger.update") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    debit: debitValue,
                    credit: creditValue
                })
            })
            .then(response => {
                // الكود الجديد - تحسين معالجة الاستجابة مع تسجيل مفصل
                console.log('Response Status:', response.status);
                console.log('Response Headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                // الكود الجديد - تسجيل مفصل للاستجابة
                console.log('Server Response Data:', data);
                if (data.success) {
                    // تحديث العرض
                    const debitCell = row.querySelector('.debit-amount');
                    const creditCell = row.querySelector('.credit-amount');
                    const editBtn = row.querySelector('.edit-btn');
                    const saveBtn = row.querySelector('.save-btn');
                    const cancelBtn = row.querySelector('.cancel-btn');

                    debitCell.innerHTML = parseFloat(debitValue).toLocaleString('en-US', {minimumFractionDigits: 2});
                    creditCell.innerHTML = parseFloat(creditValue).toLocaleString('en-US', {minimumFractionDigits: 2});

                    // إظهار/إخفاء الأزرار
                    editBtn.style.display = 'inline-block';
                    saveBtn.style.display = 'none';
                    cancelBtn.style.display = 'none';

                    // إظهار رسالة نجاح
                    alert('{{ __("Transaction updated successfully!") }}');

                    // إعادة تحميل الصفحة لتحديث الأرصدة
                    location.reload();
                } else {
                    alert('{{ __("Error: ") }}' + data.message);
                }
            })
            .catch(error => {
                // الكود القديم - معالجة أخطاء بسيطة
                // console.error('Error:', error);
                // alert('{{ __("An error occurred while updating the transaction.") }}');

                // الكود الجديد - معالجة أخطاء مفصلة لتسهيل التشخيص
                console.error('Full Error Object:', error);
                console.error('Error Details:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });

                // محاولة قراءة تفاصيل الخطأ من الاستجابة
                if (error.response) {
                    error.response.text().then(text => {
                        console.error('Server Response:', text);
                        try {
                            const jsonResponse = JSON.parse(text);
                            console.error('Parsed Response:', jsonResponse);
                            if (jsonResponse.debug_info) {
                                console.error('Debug Info:', jsonResponse.debug_info);
                            }
                        } catch (parseError) {
                            console.error('Could not parse server response as JSON');
                        }
                    });
                }

                alert('{{ __("An error occurred while updating the transaction. Check console for details.") }}');
            });
        }
    </script>
@endpush

@section('action-btn')
    <div class="float-end">
        {{--        <a class="btn btn-sm btn-primary" data-bs-toggle="collapse" href="#multiCollapseExample1" role="button" aria-expanded="false" aria-controls="multiCollapseExample1" data-bs-toggle="tooltip" title="{{__('Filter')}}"> --}}
        {{--            <i class="ti ti-filter"></i> --}}
        {{--        </a> --}}

        <a href="#" class="btn btn-sm btn-primary" onclick="saveAsPDF()"data-bs-toggle="tooltip"
            title="{{ __('Download') }}" data-original-title="{{ __('Download') }}">
            <span class="btn-inner--icon"><i class="ti ti-download"></i></span>
        </a>

    </div>
@endsection

@php
        $selectAcc =     [[
    "id" => 0,
    "code" => '',
    "name" => "Select",
    "parent" => 0,
]];
       $accounts =  array_merge($selectAcc, $accounts);
@endphp
@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="mt-2 " id="multiCollapseExample1">
                <div class="card">
                    <div class="card-body">
                        {{ Form::open(['route' => ['report.ledger'], 'method' => 'GET', 'id' => 'report_ledger']) }}

                        <div class="row align-items-center justify-content-end">
                            <div class="col-xl-10">
                                <div class="row">
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                            {{ Form::label('start_date', __('Start Date'), ['class' => 'form-label']) }}
                                            {{ Form::date('start_date', $filter['startDateRange'], ['class' => 'month-btn form-control']) }}
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                            {{ Form::label('end_date', __('End Date'), ['class' => 'form-label']) }}
                                            {{ Form::date('end_date', $filter['endDateRange'], ['class' => 'month-btn form-control']) }}
                                        </div>
                                    </div>



                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                            {{ Form::label('account', __('Account'), ['class' => 'form-label']) }}
                                            {{-- {{ Form::select('account', $accounts, isset($_GET['account']) ? $_GET['account'] : '', ['class' => 'form-control select']) }} --}}
                                            <select name="account" class="form-control" required="required">
                                                @foreach ($accounts as $chartAccount)
                                                    <option value="{{ $chartAccount['id'] }}" class="subAccount" {{ isset($_GET['account']) && $chartAccount['id'] == $_GET['account'] ? 'selected' : ''}}>{{ $chartAccount['name'] }}</option>
                                                    @foreach ($subAccounts as $subAccount)
                                                        @if ($chartAccount['id'] == $subAccount['account'])
                                                            <option value="{{ $subAccount['id'] }}" class="ms-5" {{ isset($_GET['account']) && $_GET['account'] == $subAccount['id'] ? 'selected' : ''}}> &nbsp; &nbsp;&nbsp; {{ $subAccount['name'] }}</option>
                                                        @endif
                                                    @endforeach
                                                @endforeach
                                            </select>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="row">
                                    <div class="col-auto mt-4">
                                        <a href="#" class="btn btn-sm btn-primary me-1"
                                            onclick="document.getElementById('report_ledger').submit(); return false;"
                                            data-bs-toggle="tooltip" title="{{ __('Apply') }}"
                                            data-original-title="{{ __('apply') }}">
                                            <span class="btn-inner--icon"><i class="ti ti-search"></i></span>
                                        </a>
                                        <a href="{{ route('report.ledger') }}" class="btn btn-sm btn-danger "
                                            data-bs-toggle="tooltip" title="{{ __('Reset') }}"
                                            data-original-title="{{ __('Reset') }}">
                                            <span class="btn-inner--icon"><i
                                                    class="ti ti-refresh text-white-off "></i></span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{ Form::close() }}
                </div>
            </div>
        </div>
    </div>



    <div id="printableArea">
        {{-- <div class="row mt-2">
            <div class="col">
                <input type="hidden"
                    value="{{ __('Ledger') . ' ' . 'Report of' . ' ' . $filter['startDateRange'] . ' to ' . $filter['endDateRange'] }}"
                    id="filename">
                <div class="card p-4 mb-4">
                    <h6 class="mb-0">{{ __('Report') }} :</h6>
                    <h7 class="text-sm mb-0">{{ __('Ledger Summary') }}</h7>
                </div>
            </div>

            <div class="col">
                <div class="card p-4 mb-4">
                    <h6 class="mb-0">{{ __('Duration') }} :</h6>
                    <h7 class="text-sm mb-0">{{ $filter['startDateRange'] . ' to ' . $filter['endDateRange'] }}</h7>
                </div>
            </div>
        </div> --}}
        {{-- @if (!empty($account))
            <div class="row mt-2">
                <div class="col">
                    <div class="card p-4 mb-4">
                        <h6 class="mb-0">{{ __('Account Name') }} :</h6>
                        <h7 class="text-sm mb-0">{{ $account->name }}</h7>
                    </div>
                </div>

                <div class="col">
                    <div class="card p-4 mb-4">
                        <h6 class="mb-0">{{ __('Account Code') }} :</h6>
                        <h7 class="text-sm mb-0">{{ $account->code }}</h7>
                    </div>
                </div>
                <div class="col">
                    <div class="card p-4 mb-4">
                        <h6 class="mb-0">{{ __('Total Debit') }} :</h6>
                        <h7 class="text-sm mb-0">{{ \Auth::user()->priceFormat($filter['debit']) }}</h7>
                    </div>
                </div>
                <div class="col">
                    <div class="card p-4 mb-4">
                        <h6 class="mb-0">{{ __('Total Credit') }} :</h6>
                        <h7 class="text-sm mb-0">{{ \Auth::user()->priceFormat($filter['credit']) }}</h7>
                    </div>
                </div>

                <div class="col">
                    <div class="card p-4 mb-4">
                        <h6 class="mb-0">{{ __('Balance') }} :</h6>
                        <h7 class="text-sm mb-0">
                            {{ $filter['balance'] > 0 ? __('Cr') . '. ' . \Auth::user()->priceFormat(abs($filter['balance'])) : __('Dr') . '. ' . \Auth::user()->priceFormat(abs($filter['balance'])) }}
                        </h7>
                    </div>
                </div>
            </div>
        @endif --}}
        <div class="row mb-4">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th> {{ __('Account Name') }}</th>
                                        <th> {{ __('Name') }}</th>
                                        <th> {{ __('Transaction Type') }}</th>
                                        <th> {{ __('Transaction Date') }}</th>
                                        <th> {{ __('Debit') }}</th>
                                        <th> {{ __('Credit') }}</th>
                                        <th> {{ __('Balance') }}</th>
                                        {{-- عمود جديد لأزرار التعديل --}}
                                        <th> {{ __('Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @php
                                        $balance = 0;
                                        $totalDebit = 0;
                                        $totalCredit = 0;

                                        $accountArrays = [];
                                        foreach ($chart_accounts as $key => $account) {
                                            $chartDatas = App\Models\Utility::getAccountData($account['id'], $filter['startDateRange'], $filter['endDateRange']);

                                            $chartDatas = $chartDatas->toArray();
                                            $accountArrays[] = $chartDatas;
                                        }
                                    @endphp

                                    @foreach ($accountArrays as $accounts)
                                        @foreach ($accounts as $account)
                                            @if ($account->reference == 'Invoice')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    </td>
                                                    <td>{{ \Auth::user()->invoiceNumberFormat($account->ids) }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                {{-- الكود القديم - التحقق البسيط من المعرف --}}
                                                {{-- <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}"> --}}

                                                {{-- الكود الجديد - تحسين التحقق من وجود معرف المعاملة --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id ?? 0 }}"
                                                    @if(!isset($account->transaction_line_id) || empty($account->transaction_line_id))
                                                        data-no-edit="true" title="{{ __('Transaction ID not available for editing') }}"
                                                    @endif>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ \Auth::user()->invoiceNumberFormat($account->ids) }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance += $total;
                                                        $totalCredit += $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                            @if ($account->reference == 'Invoice Payment')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    </td>
                                                    <td>{{ \Auth::user()->invoiceNumberFormat($account->ids) }}{{ __(' Manually Payment') }}
                                                    </td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ \Auth::user()->invoiceNumberFormat($account->ids) }}{{ __(' Manually Payment') }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                            @if ($account->reference == 'Revenue')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name}}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ __(' Revenue') }}
                                                    </td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name}}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ __(' Revenue') }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance += $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                             @if (
                                                $account->reference == 'Bill' ||
                                                    $account->reference == 'Bill Account' ||
                                                    $account->reference == 'Expense' ||
                                                    $account->reference == 'Expense Account')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    @if ($account->reference == 'Bill' || $account->reference == 'Bill Account')
                                                        <td>{{ \Auth::user()->billNumberFormat($account->ids) }}
                                                        @else
                                                        <td>{{ \Auth::user()->expenseNumberFormat($account->ids) }}
                                                    @endif
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    @if ($account->reference == 'Bill' || $account->reference == 'Bill Account')
                                                        <td>{{ \Auth::user()->billNumberFormat($account->ids) }}</td>
                                                        @else
                                                        <td>{{ \Auth::user()->expenseNumberFormat($account->ids) }}</td>
                                                    @endif
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                            @if ($account->reference == 'Bill Payment' || $account->reference == 'Expense Payment')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    @if ($account->reference == 'Bill Payment')
                                                        <td>{{ \Auth::user()->billNumberFormat($account->ids) }}{{ __(' Manually Payment') }}
                                                        @else
                                                        <td>{{ \Auth::user()->expenseNumberFormat($account->ids) }}{{ __(' Manually Payment') }}
                                                    @endif
                                                    </td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    @if ($account->reference == 'Bill Payment')
                                                        <td>{{ \Auth::user()->billNumberFormat($account->ids) }}{{ __(' Manually Payment') }}</td>
                                                        @else
                                                        <td>{{ \Auth::user()->expenseNumberFormat($account->ids) }}{{ __(' Manually Payment') }}</td>
                                                    @endif
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                            @if ($account->reference == 'Payment')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ __('Payment') }}
                                                    </td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ __('Payment') }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                            @if ($account->reference == 'POS')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ \Auth::user()->posNumberFormat($account->ids) }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ \Auth::user()->posNumberFormat($account->ids) }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance += $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                            @if ($account->reference == 'EXP')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ \Auth::user()->expenseNumberFormat($account->ids) }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ $account->user_name }}</td>
                                                    <td>{{ \Auth::user()->expenseNumberFormat($account->ids) }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif

                                            @if ($account->reference == 'Journal')
                                                {{-- الكود القديم محفوظ ومعلق --}}
                                                {{-- <tr>
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ '-' }}
                                                    </td>
                                                    <td>{{ AUth::user()->journalNumberFormat($account->reference_id) }}
                                                    </td>
                                                    <td>{{ $account->date }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                </tr> --}}

                                                {{-- الكود الجديد مع خاصية التعديل --}}
                                                <tr data-transaction-id="{{ $account->transaction_line_id ?? $account->id }}">
                                                    <td>{{ $account->account_name }}</td>
                                                    <td>{{ '-' }}</td>
                                                    <td>{{ AUth::user()->journalNumberFormat($account->reference_id) }}</td>
                                                    <td>{{ $account->date }}</td>
                                                    <td class="debit-amount">{{ \Auth::user()->priceFormat($account->debit) }}</td>
                                                    @php
                                                        $total = $account->credit - $account->debit;
                                                        $balance += $total;
                                                    @endphp
                                                    <td class="credit-amount">{{ \Auth::user()->priceFormat($account->credit) }}</td>
                                                    <td>{{ \Auth::user()->priceFormat($balance) }}</td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                title="{{ __('Edit Transaction') }}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Save Changes') }}">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit({{ $account->transaction_line_id ?? $account->id }})"
                                                                style="display: none;"
                                                                title="{{ __('Cancel') }}">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            @endif
                                        @endforeach
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
