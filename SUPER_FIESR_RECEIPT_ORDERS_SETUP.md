# إضافة شاشة أوامر الاستلام لمستخدم SUPER FIESR في قسم إدارة عمليات الفروع

## التغييرات المطبقة

### 1. تعديل القائمة الجانبية
تم تعديل ملف `resources/views/partials/admin/menu.blade.php` لإضافة شاشة "أوامر الاستلام" في قسم "إدارة عمليات الفروع".

**التغيير:**
```php
// إضافة أوامر الاستلام في قسم إدارة عمليات الفروع
<!-- 9. أوامر الاستلام - إدارة عمليات الفروع - متاح لـ SUPER FIESR -->
@if($isSuperFiesr || $isSuperFiesrBig || $isCompany || $isAccountant)
<li class="dash-item {{ Request::route()->getName() == 'receipt-order.index' || Request::route()->getName() == 'receipt-order.create' || Request::route()->getName() == 'receipt-order.edit' || Request::route()->getName() == 'receipt-order.show' ? ' active' : '' }}">
    <a class="dash-link" href="{{ route('receipt-order.index') }}">أوامر الاستلام</a>
</li>
@endif
```

### 2. إنشاء Migration للصلاحيات
تم إنشاء ملف `database/migrations/2024_12_21_000002_add_warehouse_permissions_to_super_fiesr.php` لإضافة صلاحيات المستودعات المطلوبة.

### 3. تحديث Seeder للصلاحيات
تم تحديث ملف `database/seeders/SuperFiesrProductPermissionSeeder.php` لإضافة صلاحيات المستودعات.

## الصلاحيات المضافة لـ SUPER FIESR

### صلاحيات المستودعات (جديدة):
- `manage warehouse` - إدارة المستودعات
- `show warehouse` - عرض المستودعات
- `create warehouse` - إنشاء مستودعات
- `edit warehouse` - تعديل المستودعات
- `delete warehouse` - حذف المستودعات

### صلاحيات المنتجات (موجودة مسبقاً):
- `manage product & service` - إدارة المنتجات والخدمات
- `create product & service` - إنشاء منتجات وخدمات جديدة
- `edit product & service` - تعديل المنتجات والخدمات
- `delete product & service` - حذف المنتجات والخدمات
- `show product & service` - عرض المنتجات والخدمات
- `show product expiry` - عرض تواريخ انتهاء الصلاحية
- `edit product expiry` - تعديل تواريخ انتهاء الصلاحية

## خطوات التطبيق

### 1. تشغيل Migration الجديد
```bash
php artisan migrate
```

### 2. تشغيل Seeder المحدث (اختياري)
```bash
php artisan db:seed --class=SuperFiesrProductPermissionSeeder
```

### 3. مسح Cache الصلاحيات
```bash
php artisan permission:cache-reset
```

### 4. مسح Cache التطبيق
```bash
php artisan cache:clear
php artisan config:clear
php artisan view:clear
```

## التحقق من التطبيق

### 1. التحقق من قاعدة البيانات
```sql
-- التحقق من وجود صلاحيات المستودعات
SELECT * FROM permissions WHERE name LIKE '%warehouse%';

-- التحقق من ربط الصلاحيات بدور SUPER FIESR
SELECT r.name as role_name, p.name as permission_name 
FROM roles r
JOIN role_has_permissions rhp ON r.id = rhp.role_id
JOIN permissions p ON rhp.permission_id = p.id
WHERE r.name = 'SUPER FIESR' AND (p.name LIKE '%warehouse%' OR p.name LIKE '%product%');
```

### 2. التحقق من الواجهة
1. سجل دخول بحساب مستخدم لديه دور SUPER FIESR
2. تحقق من ظهور "أوامر الاستلام" في قسم "إدارة عمليات الفروع"
3. تحقق من إمكانية الوصول إلى:
   - عرض أوامر الاستلام (`/receipt-order`)
   - إنشاء أمر استلام جديد
   - تعديل أوامر الاستلام الموجودة

## الشاشات المتاحة الآن لـ SUPER FIESR

### ✅ الأقسام المتاحة:
1. **النماذج (Forms System)**
   - عرض النماذج

2. **إدارة المنتجات (Products System)**
   - ✅ Product & Services (المنتجات والخدمات)
   - ❌ Product Stock (مخزون المنتجات) - مخفي

3. **إدارة عمليات الفروع** ⭐ **محدث**
   - الفواتير
   - تسجيل المندوبين والمردين
   - ✅ **أوامر الاستلام** ⭐ **جديد**

4. **نظام المراسلات بالشركة**
   - نظام الدعم

### ❌ الأقسام المحظورة:
- نظام المحاسبة
- نظام نقاط البيع (POS)
- إدارة العمليات المالية (عدا أوامر الاستلام)
- مخزون المنتجات (Product Stock)

## وظائف أوامر الاستلام المتاحة لـ SUPER FIESR

### ✅ العمليات المتاحة:
1. **عرض جميع أوامر الاستلام**
2. **إنشاء أمر استلام جديد**
3. **تعديل أوامر الاستلام**
4. **حذف أوامر الاستلام**
5. **عرض تفاصيل أمر الاستلام**

### 📋 أنواع أوامر الاستلام:
- **استلام بضاعة** - استلام منتجات من المورد
- **نقل بضاعة** - نقل منتجات بين المستودعات
- **أمر إخراج** - إخراج منتجات من المستودع

## ملاحظات مهمة

1. **الأمان**: تم الحفاظ على جميع فحوصات الأمان الأخرى
2. **التوافق**: التغييرات متوافقة مع باقي الأدوار
3. **المرونة**: يمكن إزالة الصلاحيات بسهولة عبر تشغيل `php artisan migrate:rollback`
4. **التكامل**: أوامر الاستلام متكاملة مع نظام المستودعات والمنتجات

## استكشاف الأخطاء

### إذا لم تظهر شاشة أوامر الاستلام:
1. تأكد من تشغيل Migration الجديد
2. تأكد من وجود صلاحيات المستودعات في قاعدة البيانات
3. امسح Cache الصلاحيات
4. تأكد من أن المستخدم لديه دور SUPER FIESR فعلاً

### إذا ظهرت رسالة "Permission denied":
1. تحقق من ربط صلاحيات المستودعات بالدور
2. تأكد من أن المستخدم مُعيَّن للدور الصحيح
3. امسح جميع أنواع Cache
4. تحقق من وجود جدول `receipt_orders` في قاعدة البيانات

## الدعم
في حالة وجود أي مشاكل، يرجى التحقق من:
- ملفات الـ logs في `storage/logs/`
- إعدادات قاعدة البيانات
- صحة ملفات Migration و Seeder
- وجود جدول `receipt_orders` في قاعدة البيانات
